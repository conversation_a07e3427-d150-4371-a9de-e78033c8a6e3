const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

/**
 * Get the current platform
 * @returns {string} Platform name: 'windows', 'macos', or 'linux'
 */
function getPlatform() {
    const platform = os.platform();
    switch (platform) {
        case 'win32':
            return 'windows';
        case 'darwin':
            return 'macos';
        default:
            return 'linux';
    }
}

/**
 * Get the VSCode configuration path based on platform
 * @param {boolean} useInsiders - Whether to use VSCode Insiders paths
 * @returns {string} Path to VSCode configuration directory
 */
function getVSCodePath(useInsiders = false) {
    const platform = getPlatform();
    const homeDir = os.homedir();
    const codeDir = useInsiders ? 'Code - Insiders' : 'Code';

    switch (platform) {
        case 'windows':
            return path.join(process.env.APPDATA, codeDir);
        case 'macos':
            return path.join(homeDir, 'Library', 'Application Support', codeDir);
        default: // linux
            return path.join(homeDir, '.config', codeDir);
    }
}

/**
 * Get the path to the VSCode machine ID file
 * @returns {string} Path to machine ID file
 */
function getMachineIdPath() {
    return path.join(getVSCodePath(), 'machineId');
}

/**
 * Get the path to VSCode extensions directory
 * @returns {string} Path to extensions directory
 */
function getExtensionsPath() {
    return path.join(getVSCodePath(), 'User', 'globalStorage');
}

/**
 * Get the path to VSCode state database
 * @returns {string} Path to state database
 */
function getStateDbPath() {
    return path.join(getVSCodePath(), 'User', 'globalStorage', 'state.vscdb');
}

/**
 * Get the backup directory path
 * @param {string} customPath - Custom backup path (optional)
 * @returns {string} Path to backup directory
 */
function getBackupDir(customPath = null) {
    if (customPath) {
        return path.join(customPath, 'vscode_resetter_backups');
    }
    return path.join(getVSCodePath(), 'resetter_backups');
}

/**
 * Create a unique backup ID based on timestamp
 * @returns {string} Unique backup ID
 */
function createBackupId() {
    const now = new Date();
    const timestamp = now.toISOString()
        .replace(/[:.]/g, '-')
        .replace('T', '_')
        .split('.')[0];
    return `backup_${timestamp}`;
}

/**
 * Generate a new random machine ID (UUID v4)
 * @returns {string} New machine ID
 */
function generateNewMachineId() {
    return crypto.randomUUID();
}

/**
 * Ensure directory exists, create if it doesn't
 * @param {string} dirPath - Directory path
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

/**
 * Copy file with error handling
 * @param {string} source - Source file path
 * @param {string} destination - Destination file path
 * @returns {boolean} Success status
 */
function copyFile(source, destination) {
    try {
        ensureDirectoryExists(path.dirname(destination));
        fs.copyFileSync(source, destination);
        return true;
    } catch (error) {
        console.error(`Failed to copy file from ${source} to ${destination}:`, error);
        return false;
    }
}

/**
 * Copy directory recursively
 * @param {string} source - Source directory path
 * @param {string} destination - Destination directory path
 * @returns {boolean} Success status
 */
function copyDirectory(source, destination) {
    try {
        ensureDirectoryExists(destination);
        
        const items = fs.readdirSync(source);
        for (const item of items) {
            const sourcePath = path.join(source, item);
            const destPath = path.join(destination, item);
            
            const stat = fs.statSync(sourcePath);
            if (stat.isDirectory()) {
                copyDirectory(sourcePath, destPath);
            } else {
                copyFile(sourcePath, destPath);
            }
        }
        return true;
    } catch (error) {
        console.error(`Failed to copy directory from ${source} to ${destination}:`, error);
        return false;
    }
}

/**
 * Remove directory recursively
 * @param {string} dirPath - Directory path to remove
 * @returns {boolean} Success status
 */
function removeDirectory(dirPath) {
    try {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
        return true;
    } catch (error) {
        console.error(`Failed to remove directory ${dirPath}:`, error);
        return false;
    }
}

/**
 * Read file content safely
 * @param {string} filePath - File path to read
 * @returns {string|null} File content or null if failed
 */
function readFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            return fs.readFileSync(filePath, 'utf8').trim();
        }
        return null;
    } catch (error) {
        console.error(`Failed to read file ${filePath}:`, error);
        return null;
    }
}

/**
 * Write file content safely
 * @param {string} filePath - File path to write
 * @param {string} content - Content to write
 * @returns {boolean} Success status
 */
function writeFile(filePath, content) {
    try {
        ensureDirectoryExists(path.dirname(filePath));
        fs.writeFileSync(filePath, content, 'utf8');
        return true;
    } catch (error) {
        console.error(`Failed to write file ${filePath}:`, error);
        return false;
    }
}

/**
 * Check if file or directory exists
 * @param {string} filePath - Path to check
 * @returns {boolean} Existence status
 */
function exists(filePath) {
    return fs.existsSync(filePath);
}

/**
 * Get list of directories in a path
 * @param {string} dirPath - Directory path
 * @returns {string[]} Array of directory names
 */
function getDirectories(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) {
            return [];
        }
        
        return fs.readdirSync(dirPath)
            .filter(item => {
                const itemPath = path.join(dirPath, item);
                return fs.statSync(itemPath).isDirectory();
            });
    } catch (error) {
        console.error(`Failed to read directories from ${dirPath}:`, error);
        return [];
    }
}

/**
 * Get file stats
 * @param {string} filePath - File path
 * @returns {object|null} File stats or null if failed
 */
function getFileStats(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            return fs.statSync(filePath);
        }
        return null;
    } catch (error) {
        console.error(`Failed to get stats for ${filePath}:`, error);
        return null;
    }
}

/**
 * Format file size in human readable format
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size
 */
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

module.exports = {
    getPlatform,
    getVSCodePath,
    getMachineIdPath,
    getExtensionsPath,
    getStateDbPath,
    getBackupDir,
    createBackupId,
    generateNewMachineId,
    ensureDirectoryExists,
    copyFile,
    copyDirectory,
    removeDirectory,
    readFile,
    writeFile,
    exists,
    getDirectories,
    getFileStats,
    formatFileSize
};
