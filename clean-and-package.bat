@echo off
echo Cleaning up npm cache and dependencies...

REM Remove node_modules and package-lock.json
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

REM Clear npm cache
npm cache clean --force

REM Remove any existing VSIX files
if exist *.vsix del *.vsix

echo Environment cleaned. Now attempting to package...

REM Try to package the extension
vsce package

echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS! Extension packaged successfully.
    echo Look for the .vsix file in the current directory.
) else (
    echo ❌ Packaging failed. Trying alternative method...
    echo.
    echo 📋 Manual Installation Instructions:
    echo 1. Open VS Code
    echo 2. Press F5 to run in development mode
    echo 3. Or copy the extension folder to:
    echo    %USERPROFILE%\.vscode\extensions\vscode-extension-resetter-1.0.0\
)

pause
