{"name": "vscode-extension-resetter", "displayName": "VSCode Extension Resetter", "description": "Reset extension tracking data completely, even after uninstallation", "version": "1.0.0", "publisher": "vscode-extension-resetter", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["extension", "reset", "tracking", "data", "privacy", "trial", "machine-id"], "activationEvents": [], "main": "./src/extension.js", "icon": "media/icon.png", "contributes": {"commands": [{"command": "vscode-extension-resetter.openPanel", "title": "Open Extension Resetter", "category": "Extension Resetter"}], "configuration": {"title": "VSCode Extension Resetter", "properties": {"vscode-extension-resetter.autoBackup": {"type": "boolean", "default": true, "description": "Automatically create backups before making changes"}, "vscode-extension-resetter.backupLocation": {"type": "string", "default": "", "description": "Custom backup location (leave empty for default)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "echo 'No compilation needed'", "watch": "echo 'No watch needed'", "pretest": "npm run compile", "test": "node ./test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@vscode/test-electron": "^2.3.0", "vsce": "^2.15.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/vscode-extension-resetter"}, "bugs": {"url": "https://github.com/your-username/vscode-extension-resetter/issues"}, "homepage": "https://github.com/your-username/vscode-extension-resetter#readme", "license": "MIT", "dependencies": {"vscode-extension-resetter": "file:"}}