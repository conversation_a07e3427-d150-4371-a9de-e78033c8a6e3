{"name": "vscode-extension-resetter", "displayName": "VSCode Extension Resetter", "description": "Reset extension tracking data, machine ID, and clean VSCode completely", "version": "1.0.0", "publisher": "vscode-extension-resetter", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Debuggers"], "keywords": ["extension", "reset", "clean", "machine-id", "tracking", "privacy"], "activationEvents": ["onCommand:vscode-extension-resetter.openPanel", "onCommand:vscode-extension-resetter.resetMachineId", "onCommand:vscode-extension-resetter.cleanExtensionData", "onCommand:vscode-extension-resetter.createBackup", "onCommand:vscode-extension-resetter.cleanAll"], "main": "./src/extension.js", "contributes": {"commands": [{"command": "vscode-extension-resetter.openPanel", "title": "Open Extension Resetter", "category": "VSCode Resetter", "icon": "$(tools)"}, {"command": "vscode-extension-resetter.resetMachineId", "title": "Reset Machine ID", "category": "VSCode Resetter", "icon": "$(refresh)"}, {"command": "vscode-extension-resetter.cleanExtensionData", "title": "Clean Extension Data", "category": "VSCode Resetter", "icon": "$(trash)"}, {"command": "vscode-extension-resetter.createBackup", "title": "Create Backup", "category": "VSCode Resetter", "icon": "$(archive)"}, {"command": "vscode-extension-resetter.cleanAll", "title": "Clean All VSCode Data", "category": "VSCode Resetter", "icon": "$(clear-all)"}], "menus": {"commandPalette": [{"command": "vscode-extension-resetter.openPanel", "when": "true"}, {"command": "vscode-extension-resetter.resetMachineId", "when": "true"}, {"command": "vscode-extension-resetter.cleanExtensionData", "when": "true"}, {"command": "vscode-extension-resetter.createBackup", "when": "true"}, {"command": "vscode-extension-resetter.cleanAll", "when": "true"}]}, "viewsContainers": {"activitybar": [{"id": "vscode-extension-resetter", "title": "VSCode Resetter", "icon": "$(tools)"}]}, "views": {"vscode-extension-resetter": [{"id": "vscode-extension-resetter.mainView", "name": "Extension Resetter", "type": "webview"}]}, "configuration": {"title": "VSCode Extension Resetter", "properties": {"vscode-extension-resetter.autoBackup": {"type": "boolean", "default": true, "description": "Automatically create backups before performing reset operations"}, "vscode-extension-resetter.confirmDestructiveActions": {"type": "boolean", "default": true, "description": "Show confirmation dialogs for destructive actions"}, "vscode-extension-resetter.backupLocation": {"type": "string", "default": "", "description": "Custom backup location (leave empty to use default VSCode directory)"}, "vscode-extension-resetter.logLevel": {"type": "string", "enum": ["debug", "info", "warning", "error"], "default": "info", "description": "Logging level for the extension"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "webpack --mode production", "watch": "webpack --mode development --watch", "test": "node ./test/runTest.js"}, "devDependencies": {"@types/node": "^18.x", "@types/vscode": "^1.74.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "vscode-test": "^1.6.1"}, "dependencies": {"better-sqlite3": "^8.14.0"}, "repository": {"type": "git", "url": "https://github.com/sumant4ssm/vsode-extension-resetter.git"}, "bugs": {"url": "https://github.com/sumant4ssm/vsode-extension-resetter/issues"}, "homepage": "https://github.com/sumant4ssm/vsode-extension-resetter#readme", "license": "MIT", "icon": "media/icon.png"}