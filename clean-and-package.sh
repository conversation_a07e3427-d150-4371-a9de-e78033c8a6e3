#!/bin/bash

echo "Cleaning up npm cache and dependencies..."

# Remove node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Clear npm cache
npm cache clean --force

# Remove any existing VSIX files
rm -f *.vsix

echo "Environment cleaned. Now attempting to package..."

# Try to package the extension
if vsce package; then
    echo "✅ SUCCESS! Extension packaged successfully."
    echo "Look for the .vsix file in the current directory."
else
    echo "❌ Packaging failed. Trying alternative method..."
    echo ""
    echo "📋 Manual Installation Instructions:"
    echo "1. Open VS Code"
    echo "2. Press F5 to run in development mode"
    echo "3. Or copy the extension folder to:"
    echo "   ~/.vscode/extensions/vscode-extension-resetter-1.0.0/"
fi
