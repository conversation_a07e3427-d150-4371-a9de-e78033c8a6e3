@echo off
echo Installing VSCode Extension Resetter manually...

set EXTENSION_DIR=%USERPROFILE%\.vscode\extensions\vscode-extension-resetter-1.0.0

echo Creating extension directory...
if not exist "%EXTENSION_DIR%" mkdir "%EXTENSION_DIR%"

echo Copying extension files...
copy package.json "%EXTENSION_DIR%\"
xcopy /E /I src "%EXTENSION_DIR%\src"
xcopy /E /I media "%EXTENSION_DIR%\media"
copy EXTENSION_README.md "%EXTENSION_DIR%\"

echo.
echo ✅ Extension installed successfully!
echo.
echo 📋 Next steps:
echo 1. Restart VS Code
echo 2. Look for VSCode Resetter icon in Activity Bar
echo 3. Or press Ctrl+Shift+P and search "VSCode Resetter"
echo.
echo The extension is now ready to use!

pause
