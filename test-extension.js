#!/usr/bin/env node

/**
 * Simple test script to verify the extension modules work correctly
 */

const path = require('path');

// Test our core modules
console.log('🧪 Testing VSCode Extension Resetter modules...\n');

try {
    // Test utils module
    console.log('1. Testing utils module...');
    const utils = require('./src/core/utils');
    console.log(`   ✅ Platform detected: ${utils.getPlatform()}`);
    console.log(`   ✅ VSCode path: ${utils.getVSCodePath()}`);
    console.log(`   ✅ Machine ID path: ${utils.getMachineIdPath()}`);
    
    // Test machine ID module
    console.log('\n2. Testing machine ID module...');
    const machineId = require('./src/core/machineId');
    const currentId = machineId.getCurrentMachineId();
    console.log(`   ✅ Current machine ID: ${currentId || 'Not found'}`);
    
    // Test extension data module
    console.log('\n3. Testing extension data module...');
    const extensionData = require('./src/core/extensionData');
    const extensions = extensionData.listExtensionData();
    console.log(`   ✅ Extensions with data: ${extensions.length}`);
    
    // Test backup module
    console.log('\n4. Testing backup module...');
    const backup = require('./src/core/backup');
    const backups = backup.listBackups();
    console.log(`   ✅ Available backups: ${backups.length}`);
    
    // Test storage cleaner module
    console.log('\n5. Testing storage cleaner module...');
    const storageCleaner = require('./src/core/storageCleaner');
    const storageInfo = storageCleaner.getStorageInfo();
    console.log(`   ✅ Global storage exists: ${storageInfo.globalStorage.exists}`);
    console.log(`   ✅ State DB exists: ${storageInfo.stateDb.exists}`);
    
    console.log('\n🎉 All modules loaded successfully!');
    console.log('\n📋 System Information:');
    console.log(`   Platform: ${utils.getPlatform()}`);
    console.log(`   Machine ID: ${currentId || 'Not found'}`);
    console.log(`   Extensions with data: ${extensions.length}`);
    console.log(`   Available backups: ${backups.length}`);
    console.log(`   Global storage size: ${storageInfo.globalStorage.formattedSize}`);
    console.log(`   State DB size: ${storageInfo.stateDb.formattedSize}`);
    
    console.log('\n✅ Extension is ready to use!');
    console.log('\n🚀 To run the extension:');
    console.log('   1. Open this folder in VS Code');
    console.log('   2. Press F5 to start debugging');
    console.log('   3. Look for the VSCode Resetter icon in the Activity Bar');
    
} catch (error) {
    console.error('❌ Error testing modules:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Make sure you\'re in the correct directory');
    console.error('   2. Run "npm install" to install dependencies');
    console.error('   3. Check that all files are present');
    process.exit(1);
}
