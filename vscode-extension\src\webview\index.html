<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}}; script-src 'nonce-{{NONCE}}';">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="{{STYLE_URI}}" rel="stylesheet">
    <title>VSCode Extension Resetter</title>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>VSCode Extension Resetter</h1>
            <p class="subtitle">Reset extension tracking data completely, even after uninstallation</p>
        </header>

        <!-- Navigation Tabs -->
        <nav class="tabs">
            <button class="tab-button active" data-tab="info">Info</button>
            <button class="tab-button" data-tab="machine-id">Machine ID</button>
            <button class="tab-button" data-tab="extensions">Extensions</button>
            <button class="tab-button" data-tab="backup">Backup/Restore</button>
            <button class="tab-button" data-tab="clean-all">Clean All</button>
        </nav>

        <!-- Tab Content -->
        <main class="content">
            <!-- Info Tab -->
            <div id="info" class="tab-content active">
                <div class="card">
                    <h2>System Information</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Platform:</label>
                            <span id="platform-info">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Current Machine ID:</label>
                            <span id="machine-id-info">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Extensions with Data:</label>
                            <span id="extension-count-info">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Available Backups:</label>
                            <span id="backup-count-info">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>Global Storage Size:</label>
                            <span id="storage-size-info">Loading...</span>
                        </div>
                        <div class="info-item">
                            <label>State Database Size:</label>
                            <span id="state-db-size-info">Loading...</span>
                        </div>
                    </div>
                    <button class="button secondary" onclick="refreshInfo()">Refresh Information</button>
                </div>
            </div>

            <!-- Machine ID Tab -->
            <div id="machine-id" class="tab-content">
                <div class="card">
                    <h2>Machine ID Management</h2>
                    <div class="info-box">
                        <h3>About Machine ID</h3>
                        <p>The Machine ID is a unique identifier used by VSCode to track installations. Resetting it will make VSCode generate a new ID, which can help with:</p>
                        <ul>
                            <li>Resetting extension trials</li>
                            <li>Preventing extension tracking</li>
                            <li>Resolving licensing issues with some extensions</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label>Current Machine ID:</label>
                        <input type="text" id="current-machine-id" readonly>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="backup-machine-id" checked>
                            Create backup before resetting (recommended)
                        </label>
                    </div>

                    <button class="button primary" onclick="resetMachineId()">Reset Machine ID</button>
                </div>
            </div>

            <!-- Extensions Tab -->
            <div id="extensions" class="tab-content">
                <div class="card">
                    <h2>Extension Data Management</h2>
                    <div class="info-box">
                        <h3>About Extension Data</h3>
                        <p>Extensions store data in VSCode's global storage, including license information, usage telemetry, and user preferences. Resetting extension data can help with resolving issues, resetting trial periods, and removing tracking information.</p>
                    </div>

                    <div class="form-group">
                        <label>Extensions with Data:</label>
                        <p style="font-size: 0.9em; color: var(--vscode-descriptionForeground); margin-bottom: 8px;">
                            💡 <strong>Tip:</strong> Click to select extensions, hold Ctrl to select multiple
                        </p>
                        <select id="extensions-list" multiple size="8">
                            <option>Loading extensions...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="backup-extensions" checked>
                            Create backup before resetting (recommended)
                        </label>
                    </div>

                    <div class="button-group">
                        <button class="button secondary" onclick="refreshExtensions()">Refresh List</button>
                        <button class="button primary" onclick="resetSelectedExtensions()">Reset Selected</button>
                        <button class="button danger" onclick="resetAllExtensions()">Reset All Extensions</button>
                    </div>
                </div>
            </div>

            <!-- Backup/Restore Tab -->
            <div id="backup" class="tab-content">
                <div class="card">
                    <h2>Backup & Restore</h2>
                    <div class="info-box">
                        <h3>About Backups</h3>
                        <p>Backups save the current state of VSCode configuration, including Machine ID, global storage data, state database, and extension data. Creating backups before making changes allows you to restore VSCode to its previous state if needed.</p>
                    </div>

                    <div class="section">
                        <h3>Create Backup</h3>
                        <button class="button primary" onclick="createBackup()">Create New Backup</button>
                    </div>

                    <div class="section">
                        <h3>Restore from Backup</h3>
                        <div class="form-group">
                            <label>Available Backups:</label>
                            <select id="backups-list" size="6">
                                <option>Loading backups...</option>
                            </select>
                        </div>
                        <div class="button-group">
                            <button class="button secondary" onclick="refreshBackups()">Refresh List</button>
                            <button class="button primary" onclick="restoreBackup()">Restore Selected</button>
                            <button class="button danger" onclick="deleteBackup()">Delete Selected</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clean All Tab -->
            <div id="clean-all" class="tab-content">
                <div class="card">
                    <h2>Complete VSCode Reset</h2>
                    <div class="warning-box">
                        <h3>⚠️ Warning: Destructive Operation</h3>
                        <p>This will completely reset VSCode to its default state, removing all extension data, machine ID, global storage, and state database. <strong>This action cannot be undone without a backup!</strong></p>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="backup-before-clean" checked>
                            Create backup before cleaning (strongly recommended)
                        </label>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="confirm-clean-all">
                            I understand this will completely reset VSCode and cannot be undone
                        </label>
                    </div>

                    <button class="button danger" onclick="cleanAll()" disabled id="clean-all-button">Clean All VSCode Data</button>
                </div>
            </div>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="status-message">Ready</span>
            </div>
            <div class="status-right">
                <span id="platform-status">Platform: Loading...</span>
                <span>VSCode Extension Resetter v1.0.0</span>
            </div>
        </footer>

        <!-- Operation Log -->
        <div class="operation-log" id="operation-log" style="display: none;">
            <div class="log-header">
                <h3>Operation Log</h3>
                <button class="button secondary small" onclick="clearLog()">Clear</button>
                <button class="button secondary small" onclick="toggleLog()">Hide</button>
            </div>
            <div class="log-content" id="log-content">
                <!-- Log entries will be added here -->
            </div>
        </div>
    </div>

    <script nonce="{{NONCE}}" src="{{SCRIPT_URI}}"></script>
</body>
</html>
