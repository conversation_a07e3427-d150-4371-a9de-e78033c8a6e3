const vscode = require('vscode');
const path = require('path');

// Import our core modules
const { getCurrentMachineId, resetMachineId, getMachineIdInfo } = require('./core/machineId');
const { listExtensionData, resetExtensionData, resetAllExtensions } = require('./core/extensionData');
const { cleanGlobalStorage, resetStateDb, cleanStorageJson, getStorageInfo } = require('./core/storageCleaner');
const { listBackups, deleteBackup } = require('./core/backup');
const { getPlatform } = require('./core/utils');

/**
 * Main extension activation function
 * @param {vscode.ExtensionContext} context 
 */
function activate(context) {
    console.log('VSCode Extension Resetter is now active!');

    // Create the webview provider
    const provider = new ExtensionResetterViewProvider(context.extensionUri);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('vscode-extension-resetter.mainView', provider)
    );

    // Register commands
    const commands = [
        vscode.commands.registerCommand('vscode-extension-resetter.openPanel', () => {
            ExtensionResetterPanel.createOrShow(context.extensionUri);
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.resetMachineId', async () => {
            await executeResetCommand('reset-machine-id');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.cleanExtensionData', async () => {
            await executeResetCommand('clean-extension-data');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.createBackup', async () => {
            await executeResetCommand('backup');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.cleanAll', async () => {
            const result = await vscode.window.showWarningMessage(
                'This will clean ALL VSCode data including extensions, settings, and machine ID. This action cannot be undone without a backup. Continue?',
                { modal: true },
                'Yes, Clean All',
                'Cancel'
            );
            
            if (result === 'Yes, Clean All') {
                await executeResetCommand('clean-all');
            }
        })
    ];

    context.subscriptions.push(...commands);
}

/**
 * Execute a reset command using our JavaScript modules
 * @param {string} command - The command to execute
 */
async function executeResetCommand(command) {
    const config = vscode.workspace.getConfiguration('vscode-extension-resetter');
    const autoBackup = config.get('autoBackup');
    const customBackupDir = config.get('backupLocation') || null;

    try {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `Executing ${command}...`,
            cancellable: false
        }, async (progress) => {
            let result;

            switch (command) {
                case 'reset-machine-id':
                    result = resetMachineId(autoBackup, customBackupDir);
                    break;

                case 'clean-extension-data':
                    result = resetAllExtensions(autoBackup, customBackupDir);
                    break;

                case 'backup':
                    // Create a comprehensive backup
                    const { backupGlobalStorage } = require('./core/storageCleaner');
                    const { backupFile } = require('./core/backup');
                    const { getMachineIdPath } = require('./core/utils');

                    const globalStorageResult = backupGlobalStorage(null, customBackupDir);
                    const machineIdResult = backupFile(getMachineIdPath(), globalStorageResult.backupId, customBackupDir);

                    result = {
                        success: globalStorageResult.success || machineIdResult.success,
                        message: `Backup created with ID: ${globalStorageResult.backupId || machineIdResult.backupId}`
                    };
                    break;

                case 'clean-all':
                    // Clean everything
                    const machineIdResetResult = resetMachineId(autoBackup, customBackupDir);
                    const globalStorageCleanResult = cleanGlobalStorage(false, customBackupDir); // Don't double backup
                    const stateDbResetResult = resetStateDb(false, customBackupDir);
                    const storageJsonCleanResult = cleanStorageJson(false, customBackupDir);

                    const allSuccess = machineIdResetResult.success &&
                                     globalStorageCleanResult.success &&
                                     stateDbResetResult.success &&
                                     storageJsonCleanResult.success;

                    result = {
                        success: allSuccess,
                        message: allSuccess ? 'All VSCode data cleaned successfully' : 'Some operations failed during cleaning'
                    };
                    break;

                default:
                    result = {
                        success: false,
                        message: `Unknown command: ${command}`
                    };
            }

            if (result.success) {
                vscode.window.showInformationMessage(result.message);
            } else {
                vscode.window.showErrorMessage(result.message);
            }
        });
    } catch (error) {
        console.error(`Error executing ${command}:`, error);
        vscode.window.showErrorMessage(`Failed to execute ${command}: ${error.message}`);
    }
}

/**
 * Webview View Provider for the sidebar
 */
class ExtensionResetterViewProvider {
    constructor(extensionUri) {
        this._extensionUri = extensionUri;
    }

    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'openPanel':
                    ExtensionResetterPanel.createOrShow(this._extensionUri);
                    break;
                case 'executeCommand':
                    await executeResetCommand(data.command);
                    break;
            }
        });
    }

    _getHtmlForWebview(webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VSCode Extension Resetter</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 10px;
                    margin: 0;
                }
                .button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    margin: 4px 0;
                    cursor: pointer;
                    width: 100%;
                    text-align: left;
                    border-radius: 2px;
                }
                .button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .button.primary {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                .button.secondary {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
                .section {
                    margin-bottom: 16px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .section:last-child {
                    border-bottom: none;
                }
                h3 {
                    margin-top: 0;
                    margin-bottom: 8px;
                    color: var(--vscode-foreground);
                }
                .description {
                    font-size: 0.9em;
                    color: var(--vscode-descriptionForeground);
                    margin-bottom: 8px;
                }
            </style>
        </head>
        <body>
            <div class="section">
                <h3>Quick Actions</h3>
                <button class="button primary" onclick="openPanel()">Open Full Interface</button>
            </div>
            
            <div class="section">
                <h3>Machine ID</h3>
                <div class="description">Reset VSCode's machine ID to prevent extension tracking</div>
                <button class="button secondary" onclick="executeCommand('reset-machine-id')">Reset Machine ID</button>
            </div>
            
            <div class="section">
                <h3>Extensions</h3>
                <div class="description">Clean extension data and tracking information</div>
                <button class="button secondary" onclick="executeCommand('clean-extension-data')">Clean Extension Data</button>
            </div>
            
            <div class="section">
                <h3>Backup</h3>
                <div class="description">Create a backup of current VSCode state</div>
                <button class="button secondary" onclick="executeCommand('backup')">Create Backup</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function openPanel() {
                    vscode.postMessage({ type: 'openPanel' });
                }
                
                function executeCommand(command) {
                    vscode.postMessage({ type: 'executeCommand', command: command });
                }
            </script>
        </body>
        </html>`;
    }
}

/**
 * Main webview panel for the full interface
 */
class ExtensionResetterPanel {
    static currentPanel;

    static createOrShow(extensionUri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (ExtensionResetterPanel.currentPanel) {
            ExtensionResetterPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'extensionResetter',
            'VSCode Extension Resetter',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'src', 'webview')]
            }
        );

        ExtensionResetterPanel.currentPanel = new ExtensionResetterPanel(panel, extensionUri);
    }

    constructor(panel, extensionUri) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        this._update();

        this._panel.onDidDispose(() => this.dispose(), null);
    }

    dispose() {
        ExtensionResetterPanel.currentPanel = undefined;
        this._panel.dispose();
    }

    _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
        
        // Handle messages from the webview
        webview.onDidReceiveMessage(async (data) => {
            try {
                switch (data.type) {
                    case 'executeCommand':
                        await this._handleWebviewCommand(data, webview);
                        break;
                    case 'getInfo':
                        const info = await this._getSystemInfo();
                        webview.postMessage({ type: 'systemInfo', data: info });
                        break;
                    case 'getExtensions':
                        const extensions = listExtensionData();
                        webview.postMessage({ type: 'extensionData', data: extensions });
                        break;
                    case 'getBackups':
                        const config = vscode.workspace.getConfiguration('vscode-extension-resetter');
                        const customBackupDir = config.get('backupLocation') || null;
                        const backups = listBackups(customBackupDir);
                        webview.postMessage({ type: 'backupData', data: backups });
                        break;
                }
            } catch (error) {
                console.error('Error handling webview message:', error);
                webview.postMessage({
                    type: 'operationResult',
                    data: { success: false, message: `Error: ${error.message}` }
                });
            }
        });
    }

    async _handleWebviewCommand(data, webview) {
        const config = vscode.workspace.getConfiguration('vscode-extension-resetter');
        const autoBackup = config.get('autoBackup');
        const customBackupDir = config.get('backupLocation') || null;

        let result;

        switch (data.command) {
            case 'reset-machine-id':
                const backup = data.options?.backup !== undefined ? data.options.backup : autoBackup;
                result = resetMachineId(backup, customBackupDir);
                break;

            case 'clean-extension-data':
                result = resetAllExtensions(autoBackup, customBackupDir);
                break;

            case 'reset-selected-extensions':
                const { resetMultipleExtensions } = require('./core/extensionData');
                const extensionIds = data.options?.extensionIds || [];
                const backupExtensions = data.options?.backup !== undefined ? data.options.backup : autoBackup;
                result = resetMultipleExtensions(extensionIds, backupExtensions, customBackupDir);
                break;

            case 'backup':
                const { backupGlobalStorage } = require('./core/storageCleaner');
                const { backupFile } = require('./core/backup');
                const { getMachineIdPath } = require('./core/utils');

                const globalStorageResult = backupGlobalStorage(null, customBackupDir);
                const machineIdResult = backupFile(getMachineIdPath(), globalStorageResult.backupId, customBackupDir);

                result = {
                    success: globalStorageResult.success || machineIdResult.success,
                    message: `Backup created with ID: ${globalStorageResult.backupId || machineIdResult.backupId}`
                };
                break;

            case 'restore-backup':
                const { restoreMachineId } = require('./core/machineId');
                const { restoreDirectory } = require('./core/backup');
                const { getExtensionsPath } = require('./core/utils');

                const backupId = data.options?.backupId;
                if (!backupId) {
                    result = { success: false, message: 'No backup ID provided' };
                    break;
                }

                // Restore machine ID and global storage
                const machineIdRestoreResult = restoreMachineId(backupId, customBackupDir);
                const globalStorageRestoreResult = restoreDirectory(backupId, 'globalStorage', getExtensionsPath(), customBackupDir);

                result = {
                    success: machineIdRestoreResult.success || globalStorageRestoreResult.success,
                    message: 'Restore completed'
                };
                break;

            case 'delete-backup':
                const deleteBackupId = data.options?.backupId;
                if (!deleteBackupId) {
                    result = { success: false, message: 'No backup ID provided' };
                    break;
                }

                result = deleteBackup(deleteBackupId, customBackupDir);
                break;

            case 'clean-all':
                const backupBeforeClean = data.options?.backup !== undefined ? data.options.backup : autoBackup;

                // Clean everything
                const machineIdResetResult = resetMachineId(backupBeforeClean, customBackupDir);
                const globalStorageCleanResult = cleanGlobalStorage(false, customBackupDir); // Don't double backup
                const stateDbResetResult = resetStateDb(false, customBackupDir);
                const storageJsonCleanResult = cleanStorageJson(false, customBackupDir);

                const allSuccess = machineIdResetResult.success &&
                                 globalStorageCleanResult.success &&
                                 stateDbResetResult.success &&
                                 storageJsonCleanResult.success;

                result = {
                    success: allSuccess,
                    message: allSuccess ? 'All VSCode data cleaned successfully' : 'Some operations failed during cleaning'
                };
                break;

            default:
                result = {
                    success: false,
                    message: `Unknown command: ${data.command}`
                };
        }

        // Send result back to webview
        webview.postMessage({
            type: 'operationResult',
            data: result
        });
    }

    async _getSystemInfo() {
        try {
            const machineIdInfo = getMachineIdInfo();
            const extensionData = listExtensionData();
            const backups = listBackups();
            const storageInfo = getStorageInfo();

            return {
                platform: getPlatform(),
                machineId: machineIdInfo.currentId || 'Not found',
                machineIdExists: machineIdInfo.exists,
                machineIdValid: machineIdInfo.isValid,
                extensionCount: extensionData.length,
                extensionsWithData: extensionData.length,
                backupCount: backups.length,
                globalStorageSize: storageInfo.globalStorage.formattedSize,
                stateDbSize: storageInfo.stateDb.formattedSize,
                globalStorageExists: storageInfo.globalStorage.exists,
                stateDbExists: storageInfo.stateDb.exists
            };
        } catch (error) {
            console.error('Error getting system info:', error);
            return {
                platform: getPlatform(),
                machineId: 'Error loading',
                machineIdExists: false,
                machineIdValid: false,
                extensionCount: 0,
                extensionsWithData: 0,
                backupCount: 0,
                globalStorageSize: '0 B',
                stateDbSize: '0 B',
                globalStorageExists: false,
                stateDbExists: false,
                error: error.message
            };
        }
    }

    _getHtmlForWebview(webview) {
        // Get URIs for webview resources
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'style.css'));
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'script.js'));

        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <title>VSCode Extension Resetter</title>
        </head>
        <body>
            <div class="container">
                <!-- Header -->
                <header class="header">
                    <h1>VSCode Extension Resetter</h1>
                    <p class="subtitle">Reset extension tracking data completely, even after uninstallation</p>
                </header>

                <!-- Navigation Tabs -->
                <nav class="tabs">
                    <button class="tab-button active" data-tab="info">Info</button>
                    <button class="tab-button" data-tab="machine-id">Machine ID</button>
                    <button class="tab-button" data-tab="extensions">Extensions</button>
                    <button class="tab-button" data-tab="backup">Backup/Restore</button>
                    <button class="tab-button" data-tab="clean-all">Clean All</button>
                </nav>

                <!-- Tab Content -->
                <main class="content">
                    <!-- Info Tab -->
                    <div id="info" class="tab-content active">
                        <div class="card">
                            <h2>System Information</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Platform:</label>
                                    <span id="platform-info">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <label>Current Machine ID:</label>
                                    <span id="machine-id-info">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <label>Extensions with Data:</label>
                                    <span id="extension-count-info">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <label>Available Backups:</label>
                                    <span id="backup-count-info">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <label>Global Storage Size:</label>
                                    <span id="storage-size-info">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <label>State Database Size:</label>
                                    <span id="state-db-size-info">Loading...</span>
                                </div>
                            </div>
                            <button class="button secondary" onclick="refreshInfo()">Refresh Information</button>
                        </div>
                    </div>

                    <!-- Machine ID Tab -->
                    <div id="machine-id" class="tab-content">
                        <div class="card">
                            <h2>Machine ID Management</h2>
                            <div class="info-box">
                                <h3>About Machine ID</h3>
                                <p>The Machine ID is a unique identifier used by VSCode to track installations. Resetting it will make VSCode generate a new ID, which can help with:</p>
                                <ul>
                                    <li>Resetting extension trials</li>
                                    <li>Preventing extension tracking</li>
                                    <li>Resolving licensing issues with some extensions</li>
                                </ul>
                            </div>

                            <div class="form-group">
                                <label>Current Machine ID:</label>
                                <input type="text" id="current-machine-id" readonly>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="backup-machine-id" checked>
                                    Create backup before resetting (recommended)
                                </label>
                            </div>

                            <button class="button primary" onclick="resetMachineId()">Reset Machine ID</button>
                        </div>
                    </div>

                    <!-- Extensions Tab -->
                    <div id="extensions" class="tab-content">
                        <div class="card">
                            <h2>Extension Data Management</h2>
                            <div class="info-box">
                                <h3>About Extension Data</h3>
                                <p>Extensions store data in VSCode's global storage, including license information, usage telemetry, and user preferences. Resetting extension data can help with resolving issues, resetting trial periods, and removing tracking information.</p>
                            </div>

                            <div class="form-group">
                                <label>Extensions with Data:</label>
                                <p style="font-size: 0.9em; color: var(--vscode-descriptionForeground); margin-bottom: 8px;">
                                    💡 <strong>Tip:</strong> Click to select extensions, hold Ctrl to select multiple
                                </p>
                                <select id="extensions-list" multiple size="8">
                                    <option>Loading extensions...</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="backup-extensions" checked>
                                    Create backup before resetting (recommended)
                                </label>
                            </div>

                            <div class="button-group">
                                <button class="button secondary" onclick="refreshExtensions()">Refresh List</button>
                                <button class="button primary" onclick="resetSelectedExtensions()">Reset Selected</button>
                                <button class="button danger" onclick="resetAllExtensions()">Reset All Extensions</button>
                            </div>
                        </div>
                    </div>

                    <!-- Backup/Restore Tab -->
                    <div id="backup" class="tab-content">
                        <div class="card">
                            <h2>Backup & Restore</h2>
                            <div class="info-box">
                                <h3>About Backups</h3>
                                <p>Backups save the current state of VSCode configuration, including Machine ID, global storage data, state database, and extension data. Creating backups before making changes allows you to restore VSCode to its previous state if needed.</p>
                            </div>

                            <div class="section">
                                <h3>Create Backup</h3>
                                <button class="button primary" onclick="createBackup()">Create New Backup</button>
                            </div>

                            <div class="section">
                                <h3>Restore from Backup</h3>
                                <div class="form-group">
                                    <label>Available Backups:</label>
                                    <select id="backups-list" size="6">
                                        <option>Loading backups...</option>
                                    </select>
                                </div>
                                <div class="button-group">
                                    <button class="button secondary" onclick="refreshBackups()">Refresh List</button>
                                    <button class="button primary" onclick="restoreBackup()">Restore Selected</button>
                                    <button class="button danger" onclick="deleteBackup()">Delete Selected</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clean All Tab -->
                    <div id="clean-all" class="tab-content">
                        <div class="card">
                            <h2>Complete VSCode Reset</h2>
                            <div class="warning-box">
                                <h3>⚠️ Warning: Destructive Operation</h3>
                                <p>This will completely reset VSCode to its default state, removing all extension data, machine ID, global storage, and state database. <strong>This action cannot be undone without a backup!</strong></p>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="backup-before-clean" checked>
                                    Create backup before cleaning (strongly recommended)
                                </label>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="confirm-clean-all">
                                    I understand this will completely reset VSCode and cannot be undone
                                </label>
                            </div>

                            <button class="button danger" onclick="cleanAll()" disabled id="clean-all-button">Clean All VSCode Data</button>
                        </div>
                    </div>
                </main>

                <!-- Status Bar -->
                <footer class="status-bar">
                    <div class="status-left">
                        <span id="status-message">Ready</span>
                    </div>
                    <div class="status-right">
                        <span id="platform-status">Platform: Loading...</span>
                        <span>VSCode Extension Resetter v1.0.0</span>
                    </div>
                </footer>
            </div>

            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }
}

/**
 * Generate a nonce for Content Security Policy
 * @returns {string} Random nonce string
 */
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

function deactivate() {}

module.exports = {
    activate,
    deactivate
};
