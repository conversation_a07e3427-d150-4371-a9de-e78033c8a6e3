const vscode = require('vscode');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

/**
 * Main extension activation function
 * @param {vscode.ExtensionContext} context 
 */
function activate(context) {
    console.log('VSCode Extension Resetter is now active!');

    // Create the webview provider
    const provider = new ExtensionResetterViewProvider(context.extensionUri);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('vscode-extension-resetter.mainView', provider)
    );

    // Register commands
    const commands = [
        vscode.commands.registerCommand('vscode-extension-resetter.openPanel', () => {
            ExtensionResetterPanel.createOrShow(context.extensionUri);
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.resetMachineId', async () => {
            await executeResetCommand('reset-machine-id');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.cleanExtensionData', async () => {
            await executeResetCommand('clean-extension-data');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.createBackup', async () => {
            await executeResetCommand('backup');
        }),
        
        vscode.commands.registerCommand('vscode-extension-resetter.cleanAll', async () => {
            const result = await vscode.window.showWarningMessage(
                'This will clean ALL VSCode data including extensions, settings, and machine ID. This action cannot be undone without a backup. Continue?',
                { modal: true },
                'Yes, Clean All',
                'Cancel'
            );
            
            if (result === 'Yes, Clean All') {
                await executeResetCommand('clean-all');
            }
        })
    ];

    context.subscriptions.push(...commands);
}

/**
 * Execute a reset command using the Python backend
 * @param {string} command - The command to execute
 */
async function executeResetCommand(command) {
    const config = vscode.workspace.getConfiguration('vscode-extension-resetter');
    const pythonPath = config.get('pythonPath') || 'python';
    const autoBackup = config.get('autoBackup');
    
    try {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `Executing ${command}...`,
            cancellable: false
        }, async (progress) => {
            return new Promise((resolve, reject) => {
                const scriptPath = path.join(__dirname, '..', 'run_cli.py');
                const args = [scriptPath, command];
                
                if (autoBackup && command !== 'backup') {
                    args.push('--backup');
                }
                
                const process = spawn(pythonPath, args, {
                    cwd: path.join(__dirname, '..')
                });
                
                let output = '';
                let error = '';
                
                process.stdout.on('data', (data) => {
                    output += data.toString();
                });
                
                process.stderr.on('data', (data) => {
                    error += data.toString();
                });
                
                process.on('close', (code) => {
                    if (code === 0) {
                        vscode.window.showInformationMessage(`${command} completed successfully!`);
                        resolve();
                    } else {
                        vscode.window.showErrorMessage(`${command} failed: ${error || 'Unknown error'}`);
                        reject(new Error(error));
                    }
                });
                
                process.on('error', (err) => {
                    vscode.window.showErrorMessage(`Failed to execute ${command}: ${err.message}`);
                    reject(err);
                });
            });
        });
    } catch (error) {
        console.error(`Error executing ${command}:`, error);
    }
}

/**
 * Webview View Provider for the sidebar
 */
class ExtensionResetterViewProvider {
    constructor(extensionUri) {
        this._extensionUri = extensionUri;
    }

    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'openPanel':
                    ExtensionResetterPanel.createOrShow(this._extensionUri);
                    break;
                case 'executeCommand':
                    await executeResetCommand(data.command);
                    break;
            }
        });
    }

    _getHtmlForWebview(webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VSCode Extension Resetter</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 10px;
                    margin: 0;
                }
                .button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    margin: 4px 0;
                    cursor: pointer;
                    width: 100%;
                    text-align: left;
                    border-radius: 2px;
                }
                .button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .button.primary {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                .button.secondary {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
                .section {
                    margin-bottom: 16px;
                    padding-bottom: 16px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                .section:last-child {
                    border-bottom: none;
                }
                h3 {
                    margin-top: 0;
                    margin-bottom: 8px;
                    color: var(--vscode-foreground);
                }
                .description {
                    font-size: 0.9em;
                    color: var(--vscode-descriptionForeground);
                    margin-bottom: 8px;
                }
            </style>
        </head>
        <body>
            <div class="section">
                <h3>Quick Actions</h3>
                <button class="button primary" onclick="openPanel()">Open Full Interface</button>
            </div>
            
            <div class="section">
                <h3>Machine ID</h3>
                <div class="description">Reset VSCode's machine ID to prevent extension tracking</div>
                <button class="button secondary" onclick="executeCommand('reset-machine-id')">Reset Machine ID</button>
            </div>
            
            <div class="section">
                <h3>Extensions</h3>
                <div class="description">Clean extension data and tracking information</div>
                <button class="button secondary" onclick="executeCommand('clean-extension-data')">Clean Extension Data</button>
            </div>
            
            <div class="section">
                <h3>Backup</h3>
                <div class="description">Create a backup of current VSCode state</div>
                <button class="button secondary" onclick="executeCommand('backup')">Create Backup</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function openPanel() {
                    vscode.postMessage({ type: 'openPanel' });
                }
                
                function executeCommand(command) {
                    vscode.postMessage({ type: 'executeCommand', command: command });
                }
            </script>
        </body>
        </html>`;
    }
}

/**
 * Main webview panel for the full interface
 */
class ExtensionResetterPanel {
    static currentPanel;

    static createOrShow(extensionUri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (ExtensionResetterPanel.currentPanel) {
            ExtensionResetterPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'extensionResetter',
            'VSCode Extension Resetter',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'src', 'webview')]
            }
        );

        ExtensionResetterPanel.currentPanel = new ExtensionResetterPanel(panel, extensionUri);
    }

    constructor(panel, extensionUri) {
        this._panel = panel;
        this._extensionUri = extensionUri;

        this._update();

        this._panel.onDidDispose(() => this.dispose(), null);
    }

    dispose() {
        ExtensionResetterPanel.currentPanel = undefined;
        this._panel.dispose();
    }

    _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
        
        // Handle messages from the webview
        webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'executeCommand':
                    await executeResetCommand(data.command);
                    break;
                case 'getInfo':
                    // Send system info back to webview
                    const info = await this._getSystemInfo();
                    webview.postMessage({ type: 'systemInfo', data: info });
                    break;
            }
        });
    }

    async _getSystemInfo() {
        // This would call the Python backend to get system information
        return {
            platform: process.platform,
            machineId: 'Loading...',
            extensionCount: 'Loading...',
            backupCount: 'Loading...'
        };
    }

    _getHtmlForWebview(webview) {
        // This will be implemented in the next step with the full HTML interface
        const htmlPath = vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'index.html');
        
        // For now, return a simple HTML structure
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VSCode Extension Resetter</title>
        </head>
        <body>
            <h1>VSCode Extension Resetter - Full Interface</h1>
            <p>Full interface will be implemented in the webview HTML file.</p>
        </body>
        </html>`;
    }
}

function deactivate() {}

module.exports = {
    activate,
    deactivate
};
