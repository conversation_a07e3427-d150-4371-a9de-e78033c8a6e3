# VS Code Extension Ignore File

# Python files (not needed for the JS extension)
*.py
*.pyc
__pycache__/
requirements.txt
setup.py
MANIFEST.in
run_cli.py
run_gui.py
vscode_extension_resetter.egg-info/

# Development files
test-extension.js
webpack.config.js
.gitignore
build_and_distribution.md

# Documentation (keep only extension readme)
README.md
docs/

# Test files
tests/

# Node modules (not needed since we have no dependencies)
node_modules/
package-lock.json

# Development artifacts
out/
*.vsix
vscode-extension-resetter-*.vsix

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
*.swp
*.swo

# Logs
*.log
npm-debug.log*

# Screenshots (too large)
screenshot.png

# Keep only essential files:
# - package.json
# - src/
# - media/
# - EXTENSION_README.md
# - LICENSE.txt
