const {
    getMachineIdPath,
    generateNewMachineId,
    readFile,
    writeFile,
    exists
} = require('./utils');

const { backupFile, restoreFile } = require('./backup');

/**
 * Get the current VSCode machine ID
 * @returns {string|null} Current machine ID or null if not found
 */
function getCurrentMachineId() {
    const machineIdPath = getMachineIdPath();
    
    if (!exists(machineIdPath)) {
        console.warn(`Machine ID file not found at ${machineIdPath}`);
        return null;
    }
    
    const machineId = readFile(machineIdPath);
    if (!machineId) {
        console.error('Failed to read machine ID file');
        return null;
    }
    
    return machineId;
}

/**
 * Reset the VSCode machine ID
 * @param {boolean} backup - Whether to create a backup before resetting
 * @param {string} customBackupDir - Custom backup directory (optional)
 * @returns {object} Result object with success status and details
 */
function resetMachineId(backup = true, customBackupDir = null) {
    const machineIdPath = getMachineIdPath();
    const oldId = getCurrentMachineId();
    let backupId = null;
    
    try {
        // Create backup if requested and file exists
        if (backup && exists(machineIdPath)) {
            const backupResult = backupFile(machineIdPath, null, customBackupDir);
            if (backupResult.success) {
                backupId = backupResult.backupId;
                console.log(`Created backup with ID: ${backupId}`);
            } else {
                console.warn('Failed to create backup, but continuing with reset');
            }
        }
        
        // Generate and write new machine ID
        const newId = generateNewMachineId();
        const writeSuccess = writeFile(machineIdPath, newId);
        
        if (writeSuccess) {
            console.log(`Machine ID reset from ${oldId || 'Not found'} to ${newId}`);
            return {
                success: true,
                backupId,
                oldId,
                newId,
                message: 'Machine ID reset successfully'
            };
        } else {
            return {
                success: false,
                backupId,
                oldId,
                newId: null,
                message: 'Failed to write new machine ID'
            };
        }
    } catch (error) {
        console.error('Error resetting machine ID:', error);
        return {
            success: false,
            backupId,
            oldId,
            newId: null,
            message: `Failed to reset machine ID: ${error.message}`
        };
    }
}

/**
 * Restore the VSCode machine ID from a backup
 * @param {string} backupId - Backup ID to restore from
 * @param {string} customBackupDir - Custom backup directory (optional)
 * @returns {object} Result object with success status and details
 */
function restoreMachineId(backupId, customBackupDir = null) {
    const machineIdPath = getMachineIdPath();
    
    try {
        const restoreResult = restoreFile(backupId, 'machineId', machineIdPath, customBackupDir);
        
        if (restoreResult.success) {
            const restoredId = getCurrentMachineId();
            console.log(`Machine ID restored to: ${restoredId}`);
            return {
                success: true,
                restoredId,
                message: 'Machine ID restored successfully'
            };
        } else {
            return {
                success: false,
                restoredId: null,
                message: restoreResult.message || 'Failed to restore machine ID'
            };
        }
    } catch (error) {
        console.error('Error restoring machine ID:', error);
        return {
            success: false,
            restoredId: null,
            message: `Failed to restore machine ID: ${error.message}`
        };
    }
}

/**
 * Validate machine ID format
 * @param {string} machineId - Machine ID to validate
 * @returns {boolean} True if valid UUID format
 */
function validateMachineId(machineId) {
    if (!machineId || typeof machineId !== 'string') {
        return false;
    }
    
    // UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(machineId);
}

/**
 * Get machine ID information
 * @returns {object} Machine ID information
 */
function getMachineIdInfo() {
    const machineIdPath = getMachineIdPath();
    const currentId = getCurrentMachineId();
    
    return {
        path: machineIdPath,
        exists: exists(machineIdPath),
        currentId,
        isValid: currentId ? validateMachineId(currentId) : false,
        lastModified: exists(machineIdPath) ? require('fs').statSync(machineIdPath).mtime : null
    };
}

module.exports = {
    getCurrentMachineId,
    resetMachineId,
    restoreMachineId,
    validateMachineId,
    getMachineIdInfo
};
