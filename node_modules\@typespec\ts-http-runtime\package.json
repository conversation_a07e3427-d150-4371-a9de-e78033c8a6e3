{"name": "@typespec/ts-http-runtime", "version": "0.2.2", "description": "Isomorphic client library for making HTTP requests in node.js and browser.", "sdk-type": "client", "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "browser": "./dist/browser/index.js", "react-native": "./dist/react-native/index.js", "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "react-native": {"types": "./dist/react-native/index.d.ts", "default": "./dist/react-native/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./internal/util": {"browser": {"types": "./dist/browser/util/internal.d.ts", "default": "./dist/browser/util/internal.js"}, "react-native": {"types": "./dist/react-native/util/internal.d.ts", "default": "./dist/react-native/util/internal.js"}, "import": {"types": "./dist/esm/util/internal.d.ts", "default": "./dist/esm/util/internal.js"}, "require": {"types": "./dist/commonjs/util/internal.d.ts", "default": "./dist/commonjs/util/internal.js"}}, "./internal/logger": {"browser": {"types": "./dist/browser/logger/internal.d.ts", "default": "./dist/browser/logger/internal.js"}, "react-native": {"types": "./dist/react-native/logger/internal.d.ts", "default": "./dist/react-native/logger/internal.js"}, "import": {"types": "./dist/esm/logger/internal.d.ts", "default": "./dist/esm/logger/internal.js"}, "require": {"types": "./dist/commonjs/logger/internal.d.ts", "default": "./dist/commonjs/logger/internal.js"}}, "./internal/policies": {"browser": {"types": "./dist/browser/policies/internal.d.ts", "default": "./dist/browser/policies/internal.js"}, "react-native": {"types": "./dist/react-native/policies/internal.d.ts", "default": "./dist/react-native/policies/internal.js"}, "import": {"types": "./dist/esm/policies/internal.d.ts", "default": "./dist/esm/policies/internal.js"}, "require": {"types": "./dist/commonjs/policies/internal.d.ts", "default": "./dist/commonjs/policies/internal.js"}}}, "files": ["dist/", "!dist/**/*.d.*ts.map", "LICENSE", "README.md"], "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "cloud"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "engines": {"node": ">=18.0.0"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/ts-http-runtime/", "sideEffects": false, "prettier": "@azure/eslint-plugin-azure-sdk/prettier.json", "scripts": {"build": "npm run clean && dev-tool run build-package && dev-tool run extract-api", "build:samples": "echo Obsolete", "build:test": "echo skipped. actual commands inlined in browser test scripts", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,cjs,mjs,json}\"", "clean": "dev-tool run vendored rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "dev-tool run build-package && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\"  \"*.{js,cjs,mjs,json}\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "lint": "eslint README.md package.json api-extractor.json src test", "lint:fix": "eslint README.md package.json api-extractor.json src test --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run clean && dev-tool run build-package && npm run unit-test:node && npm run unit-test:browser && npm run integration-test", "test:browser": "npm run clean && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && dev-tool run build-package && npm run unit-test:node && npm run integration-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "npm run clean && dev-tool run build-package && dev-tool run build-test && dev-tool run test:vitest --no-test-proxy --browser", "unit-test:node": "dev-tool run test:vitest --no-test-proxy", "update-snippets": "dev-tool run update-snippets"}, "//metadata": {"constantPaths": [{"path": "src/constants.ts", "prefix": "SDK_VERSION"}], "sampleConfiguration": {"skipFolder": true, "disableDocsMs": true, "productName": "HTTP Runtime for Generated TypeSpec Clients", "productSlugs": ["TypeSpec"]}, "migrationDate": "2023-03-08T18:36:03.000Z"}, "dependencies": {"http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "devDependencies": {"@azure-tools/vite-plugin-browser-test-map": "^1.0.0", "@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@types/node": "^18.0.0", "@vitest/browser": "^3.0.9", "@vitest/coverage-istanbul": "^3.0.9", "eslint": "^9.9.0", "playwright": "^1.41.2", "typescript": "~5.8.2", "vitest": "^3.0.9", "tsx": "^4.19.1"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./internal/util": "./src/util/internal.ts", "./internal/logger": "./src/logger/internal.ts", "./internal/policies": "./src/policies/internal.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser", "react-native"], "selfLink": false, "project": "./tsconfig.src.json"}, "module": "./dist/esm/index.js"}