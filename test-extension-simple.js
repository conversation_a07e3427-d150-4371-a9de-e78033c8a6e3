#!/usr/bin/env node

/**
 * Simple test to verify extension modules work
 */

console.log('🧪 Testing VSCode Extension Resetter...\n');

try {
    // Test basic module loading
    const utils = require('./src/core/utils');
    console.log('✅ Utils module loaded');
    console.log(`   Platform: ${utils.getPlatform()}`);
    
    const machineId = require('./src/core/machineId');
    console.log('✅ Machine ID module loaded');
    
    const extensionData = require('./src/core/extensionData');
    console.log('✅ Extension Data module loaded');
    
    const backup = require('./src/core/backup');
    console.log('✅ Backup module loaded');
    
    const storageCleaner = require('./src/core/storageCleaner');
    console.log('✅ Storage Cleaner module loaded');
    
    console.log('\n🎉 All modules working! Extension is ready.');
    console.log('\n📋 To use the extension:');
    console.log('1. Open this folder in VS Code');
    console.log('2. Press F5 (ignore any Markdown dialog - click Cancel)');
    console.log('3. Look for VSCode Resetter icon in Activity Bar');
    console.log('4. Or use Ctrl+Shift+P and search "VSCode Resetter"');
    
} catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n🔧 Make sure you\'re in the correct directory');
}
