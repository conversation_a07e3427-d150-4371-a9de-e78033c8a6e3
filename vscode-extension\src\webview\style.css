/* VSCode Extension Resetter Webview Styles */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    line-height: 1.5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.header h1 {
    font-size: 2.5em;
    font-weight: 300;
    margin-bottom: 10px;
    color: var(--vscode-foreground);
}

.subtitle {
    font-size: 1.1em;
    color: var(--vscode-descriptionForeground);
    font-weight: 300;
}

/* Navigation Tabs */
.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 1em;
    color: var(--vscode-tab-inactiveForeground);
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: var(--vscode-tab-activeForeground);
    background-color: var(--vscode-tab-hoverBackground);
}

.tab-button.active {
    color: var(--vscode-tab-activeForeground);
    border-bottom-color: var(--vscode-tab-activeBorder);
    background-color: var(--vscode-tab-activeBackground);
}

/* Content */
.content {
    flex: 1;
    margin-bottom: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Cards */
.card {
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card h2 {
    margin-bottom: 20px;
    color: var(--vscode-foreground);
    font-weight: 500;
    font-size: 1.5em;
}

.card h3 {
    margin-bottom: 12px;
    color: var(--vscode-foreground);
    font-weight: 500;
    font-size: 1.2em;
}

/* Info Boxes */
.info-box {
    background-color: var(--vscode-textBlockQuote-background);
    border-left: 4px solid var(--vscode-textBlockQuote-border);
    padding: 16px;
    margin-bottom: 20px;
    border-radius: 0 4px 4px 0;
}

.warning-box {
    background-color: var(--vscode-inputValidation-warningBackground);
    border-left: 4px solid var(--vscode-inputValidation-warningBorder);
    padding: 16px;
    margin-bottom: 20px;
    border-radius: 0 4px 4px 0;
}

.warning-box h3 {
    color: var(--vscode-inputValidation-warningForeground);
    margin-bottom: 8px;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
}

.info-item label {
    font-weight: 500;
    color: var(--vscode-foreground);
}

.info-item span {
    color: var(--vscode-descriptionForeground);
    font-family: var(--vscode-editor-font-family);
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--vscode-foreground);
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 8px 12px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    color: var(--vscode-input-foreground);
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-font-size);
}

.form-group select[multiple] {
    min-height: 120px;
}

.form-group select[multiple] option:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.form-group select[multiple] option:checked {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.form-group input[type="text"]:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Buttons */
.button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: var(--vscode-font-size);
    font-weight: 500;
    transition: all 0.2s ease;
    margin-right: 8px;
    margin-bottom: 8px;
}

.button:last-child {
    margin-right: 0;
}

.button.primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.button.primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.button.secondary {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.button.secondary:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.button.danger {
    background-color: var(--vscode-errorForeground);
    color: var(--vscode-editor-background);
}

.button.danger:hover {
    background-color: var(--vscode-errorForeground);
    opacity: 0.9;
}

.button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* Sections */
.section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid var(--vscode-panel-border);
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
}

.status-right {
    display: flex;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: 1;
        min-width: 120px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .button-group {
        flex-direction: column;
    }

    .button {
        margin-right: 0;
        width: 100%;
    }

    .status-bar {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .status-right {
        flex-direction: column;
        gap: 4px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-content.active {
    animation: fadeIn 0.3s ease;
}

/* Scrollbar Styling */
select {
    scrollbar-width: thin;
    scrollbar-color: var(--vscode-scrollbarSlider-background) var(--vscode-editor-background);
}

select::-webkit-scrollbar {
    width: 8px;
}

select::-webkit-scrollbar-track {
    background: var(--vscode-editor-background);
}

select::-webkit-scrollbar-thumb {
    background-color: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

select::-webkit-scrollbar-thumb:hover {
    background-color: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Status Message Types */
.status-success {
    color: var(--vscode-terminal-ansiGreen);
}

.status-error {
    color: var(--vscode-errorForeground);
}

.status-warning {
    color: var(--vscode-terminal-ansiYellow);
}

/* Operation Log */
.operation-log {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 300px;
    background-color: var(--vscode-panel-background);
    border-top: 2px solid var(--vscode-panel-border);
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
}

.log-header h3 {
    margin: 0;
    font-size: 14px;
    color: var(--vscode-titleBar-activeForeground);
}

.log-content {
    max-height: 250px;
    overflow-y: auto;
    padding: 8px;
}

.log-entry {
    padding: 6px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
    border-left: 3px solid transparent;
}

.log-entry.success {
    background-color: rgba(0, 255, 0, 0.1);
    border-left-color: var(--vscode-terminal-ansiGreen);
    color: var(--vscode-terminal-ansiGreen);
}

.log-entry.error {
    background-color: rgba(255, 0, 0, 0.1);
    border-left-color: var(--vscode-errorForeground);
    color: var(--vscode-errorForeground);
}

.log-entry.info {
    background-color: rgba(0, 123, 255, 0.1);
    border-left-color: var(--vscode-terminal-ansiBlue);
    color: var(--vscode-foreground);
}

.log-entry-time {
    font-size: 10px;
    opacity: 0.7;
    margin-right: 8px;
}

.log-entry-message {
    font-weight: 500;
}

.log-entry-details {
    font-size: 11px;
    opacity: 0.8;
    margin-top: 2px;
    font-family: var(--vscode-editor-font-family);
}

.button.small {
    padding: 4px 8px;
    font-size: 11px;
    margin-left: 8px;
}
