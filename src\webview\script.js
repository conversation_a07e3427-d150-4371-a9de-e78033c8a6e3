// VSCode Extension Resetter Webview Script

// Get the VS Code API
const vscode = acquireVsCodeApi();

// Global state
let systemInfo = {};
let extensionData = [];
let backupData = [];

// Initialize the webview
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeEventListeners();
    loadInitialData();
    updateButtonStates(); // Set initial button states
});

/**
 * Initialize tab functionality
 */
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Clean all confirmation checkbox
    const confirmCheckbox = document.getElementById('confirm-clean-all');
    const cleanAllButton = document.getElementById('clean-all-button');
    
    if (confirmCheckbox && cleanAllButton) {
        confirmCheckbox.addEventListener('change', function() {
            cleanAllButton.disabled = !this.checked;
        });
    }

    // Listen for messages from the extension
    window.addEventListener('message', event => {
        const message = event.data;
        
        switch (message.type) {
            case 'systemInfo':
                handleSystemInfo(message.data);
                break;
            case 'extensionData':
                handleExtensionData(message.data);
                break;
            case 'backupData':
                handleBackupData(message.data);
                break;
            case 'operationResult':
                handleOperationResult(message.data);
                break;
        }
    });
}

/**
 * Load initial data
 */
function loadInitialData() {
    setStatus('Loading system information...');

    // Load all data types
    vscode.postMessage({ type: 'getInfo' });
    vscode.postMessage({ type: 'getExtensions' });
    vscode.postMessage({ type: 'getBackups' });

    // Set a timeout to show fallback data if loading takes too long
    setTimeout(() => {
        if (!systemInfo.platform) {
            console.log('Loading timeout, showing fallback data');
            handleSystemInfo({
                platform: 'Unknown',
                machineId: 'Loading failed - try refresh',
                extensionsWithData: 0,
                backupCount: 0,
                globalStorageSize: '0 B',
                stateDbSize: '0 B'
            });
        }
        if (!extensionData || extensionData.length === 0) {
            handleExtensionData([]);
        }
        if (!backupData || backupData.length === 0) {
            handleBackupData([]);
        }
    }, 3000); // 3 second timeout
}

/**
 * Handle system information update
 */
function handleSystemInfo(data) {
    systemInfo = data;
    updateSystemInfoDisplay();
    updateMachineIdDisplay();
    updateStatusBar();
    setStatus('Ready');
}

/**
 * Handle extension data update
 */
function handleExtensionData(data) {
    extensionData = data;
    updateExtensionsDisplay();
}

/**
 * Handle backup data update
 */
function handleBackupData(data) {
    backupData = data;
    updateBackupsDisplay();
}

/**
 * Handle operation result
 */
function handleOperationResult(data) {
    if (data.success) {
        setStatus(data.message, 'success');
        addLogEntry('success', data.message, data.details);

        // Refresh data after successful operation
        setTimeout(() => {
            vscode.postMessage({ type: 'getInfo' });
            vscode.postMessage({ type: 'getExtensions' });
        }, 1000);
    } else {
        setStatus(data.message, 'error');
        addLogEntry('error', data.message, data.details);
    }

    // Show log if it's hidden
    showLog();
}

/**
 * Update system information display
 */
function updateSystemInfoDisplay() {
    document.getElementById('platform-info').textContent = systemInfo.platform || 'Unknown';
    document.getElementById('machine-id-info').textContent = systemInfo.machineId || 'Not found';
    document.getElementById('extension-count-info').textContent = systemInfo.extensionsWithData || '0';
    document.getElementById('backup-count-info').textContent = systemInfo.backupCount || '0';
    document.getElementById('storage-size-info').textContent = systemInfo.globalStorageSize || '0 B';
    document.getElementById('state-db-size-info').textContent = systemInfo.stateDbSize || '0 B';
}

/**
 * Update machine ID display
 */
function updateMachineIdDisplay() {
    const machineIdInput = document.getElementById('current-machine-id');
    if (machineIdInput) {
        machineIdInput.value = systemInfo.machineId || 'Not found';
    }
}

/**
 * Update extensions display
 */
function updateExtensionsDisplay() {
    const extensionsList = document.getElementById('extensions-list');
    if (!extensionsList) return;

    extensionsList.innerHTML = '';

    if (!extensionData || extensionData.length === 0) {
        const option = document.createElement('option');
        option.textContent = 'No extensions with data found';
        option.disabled = true;
        extensionsList.appendChild(option);
    } else {
        extensionData.forEach(ext => {
            const option = document.createElement('option');
            option.value = ext.id;
            option.textContent = `${ext.id} (${ext.formattedSize || 'Unknown size'})`;
            extensionsList.appendChild(option);
        });
    }

    // Add event listener for selection changes
    extensionsList.addEventListener('change', updateButtonStates);
}

/**
 * Update button states based on selection
 */
function updateButtonStates() {
    const extensionsList = document.getElementById('extensions-list');
    const resetSelectedBtn = document.querySelector('button[onclick="resetSelectedExtensions()"]');

    if (extensionsList && resetSelectedBtn) {
        const hasSelection = extensionsList.selectedOptions.length > 0;
        resetSelectedBtn.style.opacity = hasSelection ? '1' : '0.6';
        resetSelectedBtn.title = hasSelection ?
            `Reset ${extensionsList.selectedOptions.length} selected extension(s)` :
            'Select extensions first';
    }
}

/**
 * Update backups display
 */
function updateBackupsDisplay() {
    const backupsList = document.getElementById('backups-list');
    if (!backupsList) return;

    backupsList.innerHTML = '';
    
    if (backupData.length === 0) {
        const option = document.createElement('option');
        option.textContent = 'No backups found';
        option.disabled = true;
        backupsList.appendChild(option);
    } else {
        backupData.forEach(backup => {
            const option = document.createElement('option');
            option.value = backup.id;
            const date = new Date(backup.created).toLocaleString();
            option.textContent = `${backup.id} (${date}, ${backup.formattedSize})`;
            backupsList.appendChild(option);
        });
    }
}

/**
 * Update status bar
 */
function updateStatusBar() {
    const platformStatus = document.getElementById('platform-status');
    if (platformStatus) {
        platformStatus.textContent = `Platform: ${systemInfo.platform || 'Unknown'}`;
    }
}

/**
 * Set status message
 */
function setStatus(message, type = 'info') {
    const statusElement = document.getElementById('status-message');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `status-${type}`;
    }
}

/**
 * Refresh system information
 */
function refreshInfo() {
    setStatus('Refreshing information...');
    vscode.postMessage({ type: 'getInfo' });
}

/**
 * Reset machine ID
 */
function resetMachineId() {
    const backup = document.getElementById('backup-machine-id').checked;
    
    if (confirm('Are you sure you want to reset the machine ID? This will generate a new unique identifier for this VSCode installation.')) {
        setStatus('Resetting machine ID...');
        vscode.postMessage({ 
            type: 'executeCommand', 
            command: 'reset-machine-id',
            options: { backup }
        });
    }
}

/**
 * Refresh extensions list
 */
function refreshExtensions() {
    setStatus('Refreshing extensions...');
    vscode.postMessage({ type: 'getExtensions' });
}

/**
 * Reset selected extensions
 */
function resetSelectedExtensions() {
    const extensionsList = document.getElementById('extensions-list');

    if (!extensionsList) {
        alert('Extension list not found. Please refresh the page.');
        return;
    }

    const selectedOptions = Array.from(extensionsList.selectedOptions);

    if (selectedOptions.length === 0) {
        alert('Please select one or more extensions to reset.\n\nTip: Click on an extension in the list to select it, or hold Ctrl to select multiple extensions.');
        return;
    }

    const backup = document.getElementById('backup-extensions')?.checked ?? true;
    const extensionIds = selectedOptions.map(option => option.value);

    console.log('Selected extensions:', extensionIds);

    if (confirm(`Are you sure you want to reset data for ${extensionIds.length} selected extension(s)?\n\nExtensions: ${extensionIds.join(', ')}\n\nThis will remove all stored data for these extensions.`)) {
        setStatus('Resetting selected extensions...');
        console.log('Sending reset-selected-extensions command:', { extensionIds, backup });
        vscode.postMessage({
            type: 'executeCommand',
            command: 'reset-selected-extensions',
            options: { extensionIds, backup }
        });
    }
}

/**
 * Reset all extensions
 */
function resetAllExtensions() {
    const backup = document.getElementById('backup-extensions')?.checked ?? true;

    if (confirm('Are you sure you want to reset data for ALL extensions?\n\nThis will remove all stored data for every extension.\n\nThis action cannot be undone without a backup.')) {
        setStatus('Resetting all extensions...');
        console.log('Sending reset-all-extensions command:', { backup });
        vscode.postMessage({
            type: 'executeCommand',
            command: 'reset-all-extensions',
            options: { backup }
        });
    }
}

/**
 * Create backup
 */
function createBackup() {
    setStatus('Creating backup...');
    vscode.postMessage({ 
        type: 'executeCommand', 
        command: 'backup'
    });
}

/**
 * Refresh backups list
 */
function refreshBackups() {
    setStatus('Refreshing backups...');
    vscode.postMessage({ type: 'getBackups' });
}

/**
 * Restore from backup
 */
function restoreBackup() {
    const backupsList = document.getElementById('backups-list');
    const selectedOption = backupsList.selectedOptions[0];
    
    if (!selectedOption || selectedOption.disabled) {
        alert('Please select a backup to restore.');
        return;
    }
    
    const backupId = selectedOption.value;
    
    if (confirm(`Are you sure you want to restore from backup "${backupId}"? This will overwrite current VSCode data.`)) {
        setStatus('Restoring from backup...');
        vscode.postMessage({ 
            type: 'executeCommand', 
            command: 'restore-backup',
            options: { backupId }
        });
    }
}

/**
 * Delete backup
 */
function deleteBackup() {
    const backupsList = document.getElementById('backups-list');
    const selectedOption = backupsList.selectedOptions[0];
    
    if (!selectedOption || selectedOption.disabled) {
        alert('Please select a backup to delete.');
        return;
    }
    
    const backupId = selectedOption.value;
    
    if (confirm(`Are you sure you want to delete backup "${backupId}"? This action cannot be undone.`)) {
        setStatus('Deleting backup...');
        vscode.postMessage({ 
            type: 'executeCommand', 
            command: 'delete-backup',
            options: { backupId }
        });
    }
}

/**
 * Clean all VSCode data
 */
function cleanAll() {
    const backup = document.getElementById('backup-before-clean').checked;
    const confirmed = document.getElementById('confirm-clean-all').checked;
    
    if (!confirmed) {
        alert('Please confirm that you understand this action cannot be undone.');
        return;
    }
    
    if (confirm('⚠️ FINAL WARNING: This will completely reset VSCode to its default state. All extension data, machine ID, and tracking information will be permanently removed. Continue?')) {
        setStatus('Cleaning all VSCode data...');
        vscode.postMessage({
            type: 'executeCommand',
            command: 'clean-all',
            options: { backup }
        });
    }
}

/**
 * Add entry to operation log
 */
function addLogEntry(type, message, details) {
    const logContent = document.getElementById('log-content');
    if (!logContent) return;

    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;

    const time = new Date().toLocaleTimeString();

    let detailsHtml = '';
    if (details) {
        if (typeof details === 'string') {
            detailsHtml = `<div class="log-entry-details">${details}</div>`;
        } else if (Array.isArray(details)) {
            detailsHtml = `<div class="log-entry-details">• ${details.join('<br>• ')}</div>`;
        } else if (typeof details === 'object') {
            const detailsList = Object.entries(details).map(([key, value]) => `${key}: ${value}`);
            detailsHtml = `<div class="log-entry-details">• ${detailsList.join('<br>• ')}</div>`;
        }
    }

    entry.innerHTML = `
        <span class="log-entry-time">[${time}]</span>
        <span class="log-entry-message">${message}</span>
        ${detailsHtml}
    `;

    logContent.appendChild(entry);

    // Auto-scroll to bottom
    logContent.scrollTop = logContent.scrollHeight;

    // Limit log entries to 50
    while (logContent.children.length > 50) {
        logContent.removeChild(logContent.firstChild);
    }
}

/**
 * Show operation log
 */
function showLog() {
    const log = document.getElementById('operation-log');
    if (log) {
        log.style.display = 'block';
    }
}

/**
 * Toggle operation log visibility
 */
function toggleLog() {
    const log = document.getElementById('operation-log');
    const button = event.target;

    if (log) {
        if (log.style.display === 'none') {
            log.style.display = 'block';
            button.textContent = 'Hide';
        } else {
            log.style.display = 'none';
            button.textContent = 'Show Log';
        }
    }
}

/**
 * Clear operation log
 */
function clearLog() {
    const logContent = document.getElementById('log-content');
    if (logContent) {
        logContent.innerHTML = '';
    }
}
